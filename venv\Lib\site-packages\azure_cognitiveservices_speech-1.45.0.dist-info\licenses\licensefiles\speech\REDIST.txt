The following libraries can be redistributed if you are using the Cognitive Services Speech SDK,
subject to the license (https://aka.ms/csspeech/license).

Microsoft.CognitiveServices.Speech.core.dll
Microsoft.CognitiveServices.Speech.csharp.dll
Microsoft.CognitiveServices.Speech.extension.audio.sys.dll
Microsoft.CognitiveServices.Speech.extension.codec.dll
Microsoft.CognitiveServices.Speech.extension.embedded.sr.dll
Microsoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dll
Microsoft.CognitiveServices.Speech.extension.embedded.tts.dll
Microsoft.CognitiveServices.Speech.extension.kws.dll
Microsoft.CognitiveServices.Speech.extension.kws.ort.dll
Microsoft.CognitiveServices.Speech.extension.lu.dll
Microsoft.CognitiveServices.Speech.extension.mas.dll
FPIEProcessor.dll
Microsoft.CognitiveServices.Speech.extension.onnxruntime.dll
Microsoft.CognitiveServices.Speech.extension.telemetry.dll
Microsoft.CognitiveServices.Speech.extension.vad.dll
Microsoft.CognitiveServices.Speech.java.bindings.dll
MicrosoftCognitiveServicesSpeech.xcframework (or content derived from it)
client-sdk-<VERSION>.aar (or content derived from it)
client-sdk-<VERSION>.jar (or content derived from it)
client-sdk-embedded-<VERSION>.aar (or content derived from it)
client-sdk-embedded-<VERSION>.jar (or content derived from it)
libMicrosoft.CognitiveServices.Speech.core.a
libMicrosoft.CognitiveServices.Speech.core.dylib
libMicrosoft.CognitiveServices.Speech.core.so
libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so
libMicrosoft.CognitiveServices.Speech.extension.codec.so
libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.dylib
libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.so
libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.dylib
libMicrosoft.CognitiveServices.Speech.extension.embedded.sr.runtime.so
libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.dylib
libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.so
libMicrosoft.CognitiveServices.Speech.extension.embedded.tts.runtime.so
libMicrosoft.CognitiveServices.Speech.extension.kws.so
libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so
libMicrosoft.CognitiveServices.Speech.extension.lu.so
libMicrosoft.CognitiveServices.Speech.extension.mas.so
libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.dylib
libMicrosoft.CognitiveServices.Speech.extension.onnxruntime.so
libMicrosoft.CognitiveServices.Speech.extension.telemetry.dylib
libMicrosoft.CognitiveServices.Speech.extension.telemetry.so
libMicrosoft.CognitiveServices.Speech.extension.vad.so
libMicrosoft.CognitiveServices.Speech.java.bindings.dylib
libMicrosoft.CognitiveServices.Speech.java.bindings.so
azure_cognitiveservices_speech-<VERSION>-*.whl

Depending on your usage, you only need to redistribute a subset of the above.
